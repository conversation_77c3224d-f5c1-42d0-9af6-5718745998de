/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    color: #FFFFFF;
    background-color: #F7F7F7;
    overflow: hidden;
}

/* 主要布局 */
.layout {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

/* 左侧菜单栏 */
.left-sidebar {
    width: 48px;
    height: 100%;
    background-color: #000000;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.sidebar-icon {
    width: 48px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.home-active {
    background-color: rgba(255, 255, 255, 0.1);
    position: relative;
}

.home-active::after {
    content: '';
    position: absolute;
    width: 3px;
    height: 70%;
    background-color: #3DA0FF;
    left: 0;
    border-radius: 0 2px 2px 0;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
    position: relative;
}

/* 顶部导航条 */
.header {
    height: 87px;
    width: 100%;
    display: flex;
    flex-direction: column;
    z-index: 10;
}

.header-main {
    height: 59px;
    width: 100%;
    background-color: #254F7A;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo {
    width: 56px;
    height: 56px;
}

.header-title {
    font-size: 20px;
    font-weight: 700;
    color: #FFFFFF;
}

.header-right {
    display: flex;
    align-items: center;
}

.datetime {
    font-size: 16px;
    color: #FFFFFF;
    margin-right: 20px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
}

.user-avatar {
    width: 24px;
    height: 24px;
    border-radius: 12px;
}

.user-name {
    font-size: 14px;
    color: #FFFFFF;
}

.sub-menu {
    height: 28px;
    padding-left: 30px;
    display: flex;
    align-items: center;
    background-color: transparent;
}

.menu-item {
    height: 28px;
    padding: 6px 10px;
    color: #606266;
    font-size: 14px;
    cursor: pointer;
    position: relative;
}

.menu-item.active {
    border-radius: 3px 3px 0 0;
    box-shadow: 0px 0px 1px rgba(136, 136, 136, 1);
}

/* 主体内容容器 */
.content-container {
    flex: 1;
    overflow: auto;
    background-image: url('images/main-bg.png');
    background-size: cover;
    background-repeat: no-repeat;
    position: relative;
    padding: 80px 45px 0;
}

/* 背景边框装饰 */
.content-container::before {
    content: '';
    position: absolute;
    width: 48px;
    height: calc(100% - 80px);
    background-image: url('images/left-bg.png');
    background-size: cover;
    left: 0;
    top: 80px;
    z-index: 1;
}

.content-container::after {
    content: '';
    position: absolute;
    width: 48px;
    height: calc(100% - 80px);
    background-image: url('images/right-bg.png');
    background-size: cover;
    right: 0;
    top: 80px;
    z-index: 1;
}

/* 顶部标题栏 */
.title-bar {
    width: 100%;
    height: 80px;
    background-image: url('images/title-bg.png');
    background-size: cover;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0;
}

.state-grid-logo {
    width: 48px;
    height: 48px;
    margin-right: 10px;
}

.platform-title {
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 2px;
    color: #FFFFFF;
    text-shadow: 0 0 10px rgba(0, 150, 255, 0.5);
    background: linear-gradient(to bottom, #3DA0FF, #E6FDFF, #68B1FF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.date-display {
    position: absolute;
    right: 20px;
    top: 16px;
    color: #A7D3FF;
    font-size: 14.75px;
}

/* 仪表盘布局 */
.dashboard {
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 100%;
    padding-bottom: 50px;
    position: relative;
}

.row {
    display: flex;
    gap: 15px;
    width: 100%;
}

/* 底部背景装饰 */
.dashboard::after {
    content: '';
    position: absolute;
    width: 1557px;
    height: 51px;
    background-image: url('images/bottom-bg.png');
    background-size: cover;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
}

/* 卡片通用样式 */
.card {
    background: rgba(0, 78, 146, 0.3);
    border-radius: 4px;
    backdrop-filter: blur(5px);
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(88, 169, 255, 0.2);
}

.card-header {
    padding: 14px 38px;
    position: relative;
}

.card-title {
    font-size: 16px;
    font-weight: 400;
    color: #FFFFFF;
    text-shadow: 0px 0px 12px rgba(193, 227, 255, 0.93);
}

.card-subtitle {
    font-size: 14px;
    font-weight: 800;
    color: #FFFFFF;
    position: absolute;
    right: 20px;
    top: 14px;
    text-shadow: 0px 0px 12px rgba(193, 227, 255, 0.53);
}

/* 公司简介卡片 */
.company-intro {
    width: 480px;
    height: 304px;
}

.selector {
    position: absolute;
    top: 0;
    right: 20px;
}

.selector-input {
    background: linear-gradient(to bottom, rgba(88, 166, 255, 0.15), rgba(81, 153, 255, 0.23));
    border-radius: 4px;
    padding: 12px 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 110px;
    cursor: pointer;
}

.selector-input.small {
    width: 90px;
    padding: 5px 7px;
}

.selector-input span {
    font-size: 13px;
    color: #3DA0FF;
}

.selector-input.small span {
    font-size: 12px;
}

.company-data {
    padding: 20px;
    background: rgba(108, 184, 255, 0.08);
    margin: 20px;
    height: calc(100% - 130px);
}

.data-item {
    padding: 0 32px;
    height: 24px;
    display: flex;
    align-items: center;
}

.data-label {
    font-size: 16px;
    color: #A7D3FF;
}

/* 数据卡片样式 */
.data-card {
    width: 208px;
    height: 102px;
    padding-bottom: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.data-card .card-header {
    width: 100%;
    text-align: center;
    padding: 5px 0;
}

.data-card .card-title {
    font-size: 16px;
}

.data-value {
    font-size: 29px;
    font-weight: 500;
    color: #5BDDFF;
    text-align: center;
    line-height: 1.2;
}

.data-unit {
    font-size: 16px;
    color: #FFFFFF;
    position: absolute;
    right: 70px;
    bottom: 20px;
}

/* 图表卡片样式 */
.revenue-overview, .settlement {
    width: 480px;
    height: 304px;
}

.chart-container {
    position: relative;
    width: 100%;
    height: calc(100% - 50px);
}

.chart {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.chart-overlay {
    position: absolute;
    top: 0;
    left: 0;
}

/* 数据概览卡片 */
.data-overview {
    width: 625px;
    height: 700px;
    position: relative;
}

.main-chart {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 用户画像分析卡片 */
.user-analysis {
    width: 480px;
    height: 304px;
}

/* 销售漏斗卡片 */
.funnel-analysis {
    width: 480px;
    height: 304px;
    position: relative;
}

.funnel-container {
    position: relative;
    width: 100%;
    height: calc(100% - 50px);
}

.funnel-chart {
    width: 320px;
    height: 170px;
    margin-left: 40px;
    margin-top: 20px;
}

.funnel-label {
    position: absolute;
    right: 50px;
    color: #FFFFFF;
    font-size: 14px;
}

.funnel-label.label-top {
    top: 85px;
}

.funnel-label.label-middle {
    top: 135px;
}

.funnel-label.label-bottom {
    top: 185px;
}

.count {
    display: block;
    font-size: 11px;
    color: #59D7FF;
    margin-top: 5px;
}

/* 图表数据卡片 */
.chart-data {
    width: 480px;
    height: 304px;
}

/* 动态图表 */
.dynamic-charts {
    width: 100%;
    height: calc(100% - 50px);
    padding: 10px;
}

/* 媒体查询 - 适配不同尺寸屏幕 */
@media (max-width: 1600px) {
    .dashboard {
        transform: scale(0.9);
        transform-origin: top left;
    }
}

@media (max-width: 1366px) {
    .dashboard {
        transform: scale(0.8);
        transform-origin: top left;
    }
}

@media (max-width: 1200px) {
    .dashboard {
        transform: scale(0.7);
        transform-origin: top left;
    }
} 