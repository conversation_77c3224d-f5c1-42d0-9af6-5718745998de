<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公司简介 - 模态窗口</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #004B7D;
            background-image: url('images/main-bg.png');
            background-size: cover;
            background-position: center;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #FFFFFF;
        }

        .modal-container {
            width: 480px;
            height: 304px;
            position: relative;
            overflow: hidden;
            border-radius: 4px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .modal-backdrop {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 78, 146, 0.3);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(88, 169, 255, 0.2);
        }

        .modal-content {
            position: relative;
            width: 100%;
            height: 100%;
            z-index: 1;
            padding: 14px 20px;
        }

        .modal-header {
            position: relative;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 16px;
            font-weight: 400;
            margin-left: 18px;
            color: #FFFFFF;
            text-shadow: 0px 0px 12px rgba(193, 227, 255, 0.93);
        }

        .selector {
            position: absolute;
            top: -40px;
            right: 20px;
        }

        .selector-input {
            background: linear-gradient(to bottom, rgba(88, 166, 255, 0.15), rgba(81, 153, 255, 0.23));
            border-radius: 4px;
            padding: 12px 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 110px;
            cursor: pointer;
        }

        .selector-input span {
            font-size: 13px;
            color: #3DA0FF;
        }

        .company-data {
            margin-top: 25px;
            padding: 20px;
            background: rgba(108, 184, 255, 0.08);
            height: calc(100% - 70px);
            border-radius: 2px;
        }

        .data-item {
            padding: 0 32px;
            height: 24px;
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .data-label {
            font-size: 16px;
            color: #A7D3FF;
        }
    </style>
</head>
<body>
    <div class="modal-container">
        <div class="modal-backdrop"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">公司简介</h3>
            </div>
            <div class="selector">
                <div class="selector-input">
                    <span>2025</span>
                    <img src="images/arrow-down.svg" alt="下拉箭头">
                </div>
            </div>
            <div class="company-data">
                <div class="data-item">
                    <span class="data-label">test123</span>
                </div>
                <div class="data-item">
                    <span class="data-label">test123</span>
                </div>
                <div class="data-item">
                    <span class="data-label">国网江西综合能源服务有限公司</span>
                </div>
                <div class="data-item">
                    <span class="data-label">成立于2025年</span>
                </div>
                <div class="data-item">
                    <span class="data-label">注册资金: 10000万元</span>
                </div>
                <div class="data-item">
                    <span class="data-label">服务客户: 200家</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 